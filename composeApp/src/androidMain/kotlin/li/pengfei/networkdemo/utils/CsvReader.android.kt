package li.pengfei.networkdemo.utils

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException

actual class CsvReader {
    private var context: Context? = null
    
    fun setContext(context: Context) {
        this.context = context
    }
    
    actual suspend fun readCsvFromAssets(fileName: String): String = withContext(Dispatchers.IO) {
        try {
            context?.assets?.open(fileName)?.bufferedReader()?.use { reader ->
                reader.readText()
            } ?: SampleData.SAMPLE_CSV
        } catch (e: IOException) {
            println("Error reading CSV file: ${e.message}")
            SampleData.SAMPLE_CSV
        }
    }
}

@Composable
fun rememberCsvReader(): CsvReader {
    val context = LocalContext.current
    return remember {
        CsvReader().apply { setContext(context) }
    }
}
