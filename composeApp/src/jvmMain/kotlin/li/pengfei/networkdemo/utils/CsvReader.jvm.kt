package li.pengfei.networkdemo.utils

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException

actual class CsvReader {
    actual suspend fun readCsvFromAssets(fileName: String): String = withContext(Dispatchers.IO) {
        try {
            // Try to read from resources first
            val resource = this::class.java.classLoader.getResourceAsStream(fileName)
            if (resource != null) {
                resource.bufferedReader().use { it.readText() }
            } else {
                // Try to read from current directory
                val file = File(fileName)
                if (file.exists()) {
                    file.readText()
                } else {
                    // Try to read from data.csv in project root
                    val dataFile = File("data.csv")
                    if (dataFile.exists()) {
                        dataFile.readText()
                    } else {
                        println("CSV file not found, using sample data")
                        SampleData.SAMPLE_CSV
                    }
                }
            }
        } catch (e: IOException) {
            println("Error reading CSV file: ${e.message}")
            SampleData.SAMPLE_CSV
        }
    }
}

@Composable
fun rememberCsvReader(): CsvReader {
    return remember { CsvReader() }
}
