package li.pengfei.networkdemo.data

import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.format.byUnicodePattern

/**
 * CSV parser for network data
 */
class CsvParser {
    
    private val dateTimeFormat = LocalDateTime.Format {
        byUnicodePattern("yyyy/M/d H:mm")
    }
    
    /**
     * Parse CSV content into a list of NetworkRecord objects
     */
    fun parseNetworkData(csvContent: String): List<NetworkRecord> {
        val lines = csvContent.lines().filter { it.isNotBlank() }
        if (lines.isEmpty()) return emptyList()
        
        // Skip header line
        val dataLines = lines.drop(1)
        
        return dataLines.mapNotNull { line ->
            try {
                parseNetworkRecord(line)
            } catch (e: Exception) {
                println("Error parsing line: $line - ${e.message}")
                null
            }
        }
    }
    
    private fun parseNetworkRecord(line: String): NetworkRecord? {
        val columns = line.split(",")
        if (columns.size < 35) return null // Minimum required columns
        
        try {
            val time = LocalDateTime.parse(columns[0], dateTimeFormat)
            val deviceState = DeviceState.fromString(columns[1])
            val ulThroughput = columns[2].toDoubleOrNull() ?: 0.0
            val dlThroughput = columns[3].toDoubleOrNull() ?: 0.0
            
            val cells = mutableListOf<CellData>()
            
            // Parse Cell1 data (columns 4-12)
            parseCell(columns, 4, 1)?.let { cells.add(it) }
            
            // Parse Cell2 data (columns 13-21)
            if (columns.size > 21) {
                parseCell(columns, 13, 2)?.let { cells.add(it) }
            }
            
            // Parse Cell3 data (columns 22-28)
            if (columns.size > 28) {
                parseCell(columns, 22, 3)?.let { cells.add(it) }
            }
            
            // Parse Cell4 data (columns 29-35)
            if (columns.size > 35) {
                parseCell(columns, 29, 4)?.let { cells.add(it) }
            }
            
            return NetworkRecord(
                time = time,
                deviceState = deviceState,
                ulThroughput = ulThroughput,
                dlThroughput = dlThroughput,
                cells = cells
            )
        } catch (e: Exception) {
            println("Error parsing record: ${e.message}")
            return null
        }
    }
    
    private fun parseCell(columns: List<String>, startIndex: Int, cellId: Int): CellData? {
        try {
            val ratStr = columns.getOrNull(startIndex)?.trim()
            if (ratStr.isNullOrEmpty()) return null
            
            val rat = NetworkTechnology.fromString(ratStr)
            val band = columns.getOrNull(startIndex + 1)?.trim() ?: ""
            val dlChannel = columns.getOrNull(startIndex + 2)?.toLongOrNull()
            
            // Parse RSRP values (4 values)
            val rsrpValues = mutableListOf<Double>()
            for (i in 3..6) {
                val rsrp = columns.getOrNull(startIndex + i)?.toDoubleOrNull()
                if (rsrp != null) {
                    rsrpValues.add(rsrp)
                }
            }
            
            val ulChannel = columns.getOrNull(startIndex + 7)?.toLongOrNull()
            
            // Parse TxPower values (variable number, usually 1-2)
            val txPowerValues = mutableListOf<Double>()
            var txIndex = startIndex + 8
            while (txIndex < columns.size && txIndex < startIndex + 10) {
                val txPower = columns.getOrNull(txIndex)?.toDoubleOrNull()
                if (txPower != null) {
                    txPowerValues.add(txPower)
                }
                txIndex++
            }
            
            return CellData(
                cellId = cellId,
                rat = rat,
                band = band,
                dlChannel = dlChannel,
                ulChannel = ulChannel,
                rsrpValues = rsrpValues,
                txPowerValues = txPowerValues
            )
        } catch (e: Exception) {
            println("Error parsing cell $cellId: ${e.message}")
            return null
        }
    }
}

/**
 * Data processor for converting network records to chart data
 */
class NetworkDataProcessor {
    
    /**
     * Convert network records to device state chart data
     */
    fun getDeviceStateData(records: List<NetworkRecord>): CategorizedChartData {
        val startTime = records.firstOrNull()?.time
        val dataPoints = records.mapIndexed { index, record ->
            ChartDataPoint(
                x = index.toFloat(), // Use index as time for simplicity
                y = record.deviceState.value.toFloat(),
                category = record.deviceState.displayName
            )
        }
        
        return CategorizedChartData(
            title = "Device State Over Time",
            xAxisLabel = "Time",
            yAxisLabel = "Device State",
            dataPoints = dataPoints,
            categories = setOf("Free", "Head", "Body")
        )
    }
    
    /**
     * Convert network records to RSRP chart data
     */
    fun getRsrpData(records: List<NetworkRecord>): CategorizedChartData {
        val dataPoints = mutableListOf<ChartDataPoint>()
        
        records.forEachIndexed { index, record ->
            record.cells.forEach { cell ->
                cell.rsrpValues.forEachIndexed { rsrpIndex, rsrp ->
                    dataPoints.add(
                        ChartDataPoint(
                            x = index.toFloat(),
                            y = rsrp.toFloat(),
                            category = cell.rat.displayName,
                            subcategory = "${cell.band}_${rsrpIndex}"
                        )
                    )
                }
            }
        }
        
        return CategorizedChartData(
            title = "RSRP Over Time",
            xAxisLabel = "Time",
            yAxisLabel = "RSRP (dBm)",
            dataPoints = dataPoints,
            categories = dataPoints.map { it.category }.toSet(),
            subcategories = dataPoints.map { it.subcategory }.toSet()
        )
    }
    
    /**
     * Convert network records to TxPower chart data
     */
    fun getTxPowerData(records: List<NetworkRecord>): CategorizedChartData {
        val dataPoints = mutableListOf<ChartDataPoint>()
        
        records.forEachIndexed { index, record ->
            record.cells.forEach { cell ->
                cell.txPowerValues.forEachIndexed { txIndex, txPower ->
                    dataPoints.add(
                        ChartDataPoint(
                            x = index.toFloat(),
                            y = txPower.toFloat(),
                            category = cell.rat.displayName,
                            subcategory = "${cell.band}_${txIndex}"
                        )
                    )
                }
            }
        }
        
        return CategorizedChartData(
            title = "Tx Power Over Time",
            xAxisLabel = "Time",
            yAxisLabel = "Tx Power (dBm)",
            dataPoints = dataPoints,
            categories = dataPoints.map { it.category }.toSet(),
            subcategories = dataPoints.map { it.subcategory }.toSet()
        )
    }
    
    /**
     * Convert network records to band chart data
     */
    fun getBandData(records: List<NetworkRecord>): CategorizedChartData {
        val dataPoints = mutableListOf<ChartDataPoint>()
        val bandToNumber = mutableMapOf<String, Float>()
        var bandCounter = 1f
        
        records.forEachIndexed { index, record ->
            record.cells.forEach { cell ->
                if (cell.band.isNotEmpty()) {
                    val bandNumber = bandToNumber.getOrPut(cell.band) { bandCounter++ }
                    dataPoints.add(
                        ChartDataPoint(
                            x = index.toFloat(),
                            y = bandNumber,
                            category = cell.band,
                            subcategory = cell.rat.displayName
                        )
                    )
                }
            }
        }
        
        return CategorizedChartData(
            title = "Bands Over Time",
            xAxisLabel = "Time",
            yAxisLabel = "Band",
            dataPoints = dataPoints,
            categories = dataPoints.map { it.category }.toSet(),
            subcategories = dataPoints.map { it.subcategory }.toSet()
        )
    }
    
    /**
     * Convert network records to throughput chart data
     */
    fun getThroughputData(records: List<NetworkRecord>, isUplink: Boolean): CategorizedChartData {
        val dataPoints = records.mapIndexed { index, record ->
            val throughput = if (isUplink) record.ulThroughput else record.dlThroughput
            ChartDataPoint(
                x = index.toFloat(),
                y = throughput.toFloat(),
                category = if (isUplink) "Uplink" else "Downlink"
            )
        }
        
        val direction = if (isUplink) "Uplink" else "Downlink"
        return CategorizedChartData(
            title = "$direction Throughput Over Time",
            xAxisLabel = "Time",
            yAxisLabel = "Throughput (Kb/s)",
            dataPoints = dataPoints,
            categories = setOf(direction)
        )
    }
    
    /**
     * Convert network records to channel chart data
     */
    fun getChannelData(records: List<NetworkRecord>): CategorizedChartData {
        val dataPoints = mutableListOf<ChartDataPoint>()
        
        records.forEachIndexed { index, record ->
            record.cells.forEach { cell ->
                // Add DL channel data
                cell.dlChannel?.let { dlChannel ->
                    dataPoints.add(
                        ChartDataPoint(
                            x = index.toFloat(),
                            y = (dlChannel / 1000).toFloat(), // Scale down for better visualization
                            category = "${cell.rat.displayName}_DL",
                            subcategory = cell.band
                        )
                    )
                }
                
                // Add UL channel data
                cell.ulChannel?.let { ulChannel ->
                    dataPoints.add(
                        ChartDataPoint(
                            x = index.toFloat(),
                            y = (ulChannel / 1000).toFloat(), // Scale down for better visualization
                            category = "${cell.rat.displayName}_UL",
                            subcategory = cell.band
                        )
                    )
                }
            }
        }
        
        return CategorizedChartData(
            title = "Channels Over Time",
            xAxisLabel = "Time",
            yAxisLabel = "Channel (scaled)",
            dataPoints = dataPoints,
            categories = dataPoints.map { it.category }.toSet(),
            subcategories = dataPoints.map { it.subcategory }.toSet()
        )
    }
}
