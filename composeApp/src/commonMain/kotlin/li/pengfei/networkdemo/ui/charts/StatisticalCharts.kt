package li.pengfei.networkdemo.ui.charts

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.multiplatform.cartesian.CartesianChartHost
import com.patrykandpatrick.vico.multiplatform.cartesian.data.CartesianChartModelProducer
import com.patrykandpatrick.vico.multiplatform.cartesian.data.lineSeries
import li.pengfei.networkdemo.data.CategorizedChartData
import li.pengfei.networkdemo.data.StatisticalType

/**
 * Histogram chart component - simplified version
 */
@Composable
fun HistogramChart(
    chartData: CategorizedChartData,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxSize().padding(16.dp)
    ) {
        Text(
            text = "Histogram - ${chartData.title}",
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.align(Alignment.CenterHorizontally).padding(bottom = 16.dp)
        )

        Card(
            modifier = Modifier.fillMaxWidth().height(400.dp),
            colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Histogram Chart",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Data points: ${chartData.dataPoints.size}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        if (chartData.categories.isNotEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))
            StatisticalLegend(
                categories = chartData.categories.toList(),
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
        }
    }
}

/**
 * PDF chart component - simplified version
 */
@Composable
fun PDFChart(
    chartData: CategorizedChartData,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxSize().padding(16.dp)
    ) {
        Text(
            text = "PDF - ${chartData.title}",
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.align(Alignment.CenterHorizontally).padding(bottom = 16.dp)
        )

        Card(
            modifier = Modifier.fillMaxWidth().height(400.dp),
            colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "PDF Chart",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Data points: ${chartData.dataPoints.size}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        if (chartData.categories.isNotEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))
            StatisticalLegend(
                categories = chartData.categories.toList(),
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
        }
    }
}

/**
 * CDF chart component - simplified version
 */
@Composable
fun CDFChart(
    chartData: CategorizedChartData,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxSize().padding(16.dp)
    ) {
        Text(
            text = "CDF - ${chartData.title}",
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.align(Alignment.CenterHorizontally).padding(bottom = 16.dp)
        )

        Card(
            modifier = Modifier.fillMaxWidth().height(400.dp),
            colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "CDF Chart",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Data points: ${chartData.dataPoints.size}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        if (chartData.categories.isNotEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))
            StatisticalLegend(
                categories = chartData.categories.toList(),
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
        }
    }
}





/**
 * Statistical chart legend component
 */
@Composable
private fun StatisticalLegend(
    categories: List<String>,
    modifier: Modifier = Modifier
) {
    val colors = remember {
        listOf(
            Color(0xFF2196F3), // Blue
            Color(0xFF4CAF50), // Green
            Color(0xFFFF9800), // Orange
            Color(0xFF9C27B0), // Purple
            Color(0xFFF44336), // Red
            Color(0xFF00BCD4), // Cyan
            Color(0xFFFFEB3B), // Yellow
            Color(0xFF795548)  // Brown
        )
    }
    
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(categories.size) { index ->
            val category = categories[index]
            val color = colors[index % colors.size]
            
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .background(color, CircleShape)
                )
                Text(
                    text = category,
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}

/**
 * Combined statistical charts view
 */
@Composable
fun StatisticalChartsView(
    originalChartData: CategorizedChartData,
    statisticalType: StatisticalType,
    modifier: Modifier = Modifier
) {
    when (statisticalType) {
        StatisticalType.HISTOGRAM -> {
            HistogramChart(
                chartData = originalChartData,
                modifier = modifier
            )
        }
        StatisticalType.PDF -> {
            PDFChart(
                chartData = originalChartData,
                modifier = modifier
            )
        }
        StatisticalType.CDF -> {
            CDFChart(
                chartData = originalChartData,
                modifier = modifier
            )
        }
    }
}
