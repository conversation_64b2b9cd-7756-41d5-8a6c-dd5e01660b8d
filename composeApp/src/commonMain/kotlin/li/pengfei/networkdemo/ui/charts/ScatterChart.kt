package li.pengfei.networkdemo.ui.charts

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.multiplatform.cartesian.CartesianChartHost
import com.patrykandpatrick.vico.multiplatform.cartesian.axis.HorizontalAxis
import com.patrykandpatrick.vico.multiplatform.cartesian.axis.VerticalAxis
import com.patrykandpatrick.vico.multiplatform.cartesian.data.CartesianChartModelProducer
import com.patrykandpatrick.vico.multiplatform.cartesian.data.lineSeries
import com.patrykandpatrick.vico.multiplatform.cartesian.layer.rememberLineCartesianLayer
import com.patrykandpatrick.vico.multiplatform.cartesian.rememberCartesianChart
import li.pengfei.networkdemo.data.CategorizedChartData
import li.pengfei.networkdemo.data.ChartDataPoint

/**
 * Composable for displaying scatter plot charts using Vico
 */
@Composable
fun ScatterChart(
    chartData: CategorizedChartData,
    modifier: Modifier = Modifier
) {
    val modelProducer = remember { CartesianChartModelProducer() }

    LaunchedEffect(chartData) {
        updateChartModel(modelProducer, chartData)
    }

    Column(
        modifier = modifier.fillMaxSize().padding(16.dp)
    ) {
        // Chart title
        Text(
            text = chartData.title,
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.align(Alignment.CenterHorizontally).padding(bottom = 16.dp)
        )

        // Chart
        CartesianChartHost(
            chart = rememberCartesianChart(
                rememberLineCartesianLayer(),
                startAxis = VerticalAxis.rememberStart(),
                bottomAxis = HorizontalAxis.rememberBottom(),
            ),
            modelProducer = modelProducer,
            modifier = Modifier.fillMaxWidth().height(400.dp)
        )

        // Legend
        if (chartData.categories.isNotEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))
            ChartLegend(
                categories = chartData.categories.toList(),
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
        }
    }
}

/**
 * Update the chart model with new data
 */
private suspend fun updateChartModel(
    modelProducer: CartesianChartModelProducer,
    chartData: CategorizedChartData
) {
    val groupedData = chartData.dataPoints.groupBy { it.category }

    modelProducer.runTransaction {
        groupedData.forEach { (category, points) ->
            lineSeries {
                points.sortedBy { it.x }.forEach { point ->
                    series(point.x, point.y)
                }
            }
        }
    }
}

/**
 * Chart legend component
 */
@Composable
private fun ChartLegend(
    categories: List<String>,
    modifier: Modifier = Modifier
) {
    val colors = remember {
        listOf(
            Color(0xFF2196F3), // Blue
            Color(0xFF4CAF50), // Green
            Color(0xFFFF9800), // Orange
            Color(0xFF9C27B0), // Purple
            Color(0xFFF44336), // Red
            Color(0xFF00BCD4), // Cyan
            Color(0xFFFFEB3B), // Yellow
            Color(0xFF795548)  // Brown
        )
    }
    
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(categories.size) { index ->
            val category = categories[index]
            val color = colors[index % colors.size]
            
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .background(color, CircleShape)
                )
                Text(
                    text = category,
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}

/**
 * Device State specific scatter chart
 */
@Composable
fun DeviceStateChart(
    chartData: CategorizedChartData,
    modifier: Modifier = Modifier
) {
    ScatterChart(
        chartData = chartData.copy(
            title = "Device State Over Time",
            yAxisLabel = "Device State (1-Free, 2-Head, 3-Body)"
        ),
        modifier = modifier
    )
}

/**
 * RSRP specific scatter chart
 */
@Composable
fun RsrpChart(
    chartData: CategorizedChartData,
    modifier: Modifier = Modifier
) {
    ScatterChart(
        chartData = chartData.copy(
            title = "RSRP Over Time by RAT and Band"
        ),
        modifier = modifier
    )
}

/**
 * Tx Power specific scatter chart
 */
@Composable
fun TxPowerChart(
    chartData: CategorizedChartData,
    modifier: Modifier = Modifier
) {
    ScatterChart(
        chartData = chartData.copy(
            title = "Tx Power Over Time by RAT and Band"
        ),
        modifier = modifier
    )
}

/**
 * Band specific scatter chart
 */
@Composable
fun BandChart(
    chartData: CategorizedChartData,
    modifier: Modifier = Modifier
) {
    ScatterChart(
        chartData = chartData.copy(
            title = "Bands Over Time"
        ),
        modifier = modifier
    )
}

/**
 * Channel specific scatter chart
 */
@Composable
fun ChannelChart(
    chartData: CategorizedChartData,
    modifier: Modifier = Modifier
) {
    ScatterChart(
        chartData = chartData.copy(
            title = "Channels Over Time by RAT"
        ),
        modifier = modifier
    )
}

/**
 * Throughput specific scatter chart
 */
@Composable
fun ThroughputChart(
    chartData: CategorizedChartData,
    isUplink: Boolean,
    modifier: Modifier = Modifier
) {
    val direction = if (isUplink) "Uplink" else "Downlink"
    ScatterChart(
        chartData = chartData.copy(
            title = "$direction Throughput Over Time"
        ),
        modifier = modifier
    )
}
