package li.pengfei.networkdemo

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import org.jetbrains.compose.ui.tooling.preview.Preview
import li.pengfei.networkdemo.ui.NetworkDataVisualizationApp
import li.pengfei.networkdemo.utils.SampleData

@Composable
@Preview
fun App() {
    MaterialTheme {
        NetworkDataVisualizationApp(
            csvContent = SampleData.SAMPLE_CSV,
            modifier = Modifier.fillMaxSize()
        )
    }
}